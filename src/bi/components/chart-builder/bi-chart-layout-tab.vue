<script setup>
import { storeToRefs } from 'pinia';
import { useBiStore } from '~/bi/store/bi.store';

const props = defineProps({
  chartType: {
    type: String,
    required: true,
  },
  chartConfig: {
    type: Object,
    required: true,
  },
  formInstance: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['seriesConfigChange']);

const colors = ['#101828', '#004EEB', '#D92D20', '#DC6803', '#039855', '#7839EE', '#4CA30D', '#0E9384', '#BA24D5', '#E31B54', '#344054', '#2E90FA', '#F97066', '#FDB022', '#32D583', '#A48AFB', '#66C61C', '#2ED3B7', '#E478FA', '#FD6F8E', '#667085', '#84CAFF', '#FDA29B', '#FEC84B', '#6CE9A6', '#DDD6FE', '#85E13A', '#5FE9D0', '#EEAAFD', '#FEA3B4'];

const bi_store = useBiStore();
const { chart_builder_data } = storeToRefs(bi_store);

const columns = computed(() => {
  return Object.keys(chart_builder_data.value[0]);
});

function onSeriesItemChange(payload, index) {
  emit('seriesConfigChange', payload, index);
}

async function onAddSeries(index) {
  await nextTick();
  emit(
    'seriesConfigChange',
    // Defaults
    {
      chart_type: props.chartType.replace('_chart', ''),
      color: colors[index],
      y_axis: 'primary',
      line_style: 'solid',
      line_width: 1,
      line_shape: 'straight',
      prefix: '',
      suffix: '',
      stack: false,
    },
    index,
  );
}

function getAxis(type) {
  // type = 'category' || 'value'
  if (props.chartConfig.orientation === 'horizontal') {
    if (type === 'category')
      return 'y';
    else return 'x';
  }
  else {
    if (type === 'category')
      return 'x';
    else return 'y';
  }
}
</script>

<template>
  <template v-if="['bar_chart', 'line_chart', 'area_chart', 'timeseries_chart'].includes(props.chartType)">
    <SelectElement
      name="layout_category"
      :label="getAxis('category') === 'x' ? 'X-axis' : 'Y-axis'"
      :items="columns"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <ListElement
      name="layout_values"
      :label="getAxis('value') === 'x' ? 'X-axis' : 'Y-axis'"
      :sort="true"
      :controls="{ add: true, remove: false, sort: true }"
      :add-classes="{
        ListElement: {
          handle: 'left-8 top-[1px] visible opacity-100 !z-0',
          add: '!bg-white !border-white !text-primary-700 hover:!scale-100 !mt-0 -ml-1',
        },
      }"
      add-text="+ Add another series"
      :min="1"
      @add="onAddSeries"
    >
      <template #default="{ index }">
        <ObjectElement
          :name="index"
        >
          <SelectElement
            v-if="!props.chartConfig?.layout_values?.[index]?.value"
            name="value"
            :native="false"
            :items="columns"
            :add-classes="{
              SelectElement: {
                select: {
                  wrapper: 'ml-4',
                },
              },
            }"
          />
          <TextElement
            v-if="props.chartConfig?.layout_values?.[index]?.value"
            name="value"
            :default="props.chartConfig?.layout_values?.[index]?.value"
            :readonly="true"
            :add-classes="{
              TextElement: {
                inputContainer: 'pl-6',
              },
            }"
          >
            <template #addon-before>
              <div
                class="w-3 h-3 rounded-full"
                :style="{ backgroundColor: props.chartConfig?.layout_values?.[index].color }"
              />
            </template>
            <template #addon-after>
              <BiSeriesContextMenu
                :series-config="props.chartConfig?.layout_values?.[index]"
                :columns="columns"
                @field-selected="onSeriesItemChange($event, index)"
              />
              <IconHawkXClose
                v-if="props.chartConfig?.layout_values?.length > 1"
                class="w-4 h-4 cursor-pointer ml-1"
                @click="props.formInstance.elements$.layout_values.remove(index)"
              />
            </template>
          </TextElement>
          <HiddenElement name="chart_type" />
          <HiddenElement name="y_axis" />
          <HiddenElement name="line_style" />
          <HiddenElement name="line_width" />
          <HiddenElement name="line_shape" />
          <HiddenElement name="prefix" />
          <HiddenElement name="suffix" />
          <HiddenElement name="stack" />
        </ObjectElement>
      </template>
    </ListElement>
    <SelectElement
      name="stack_by"
      label="Stack by"
      default="none"
      :items="[
        { value: 'none', label: 'None' },
        ...columns,
      ]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <SelectElement
      name="aggregation"
      label="Aggregation"
      :items="{
        sum: 'Sum',
        mean: 'Average',
        median: 'Median',
        min: 'Minimum',
        max: 'Maximum',
        count: 'Count',
        first: 'First',
        last: 'Last',
      }"
      default="sum"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
  </template>
  <template v-else-if="['pie_chart', 'doughnut_chart'].includes(props.chartType)">
    <SelectElement
      name="layout_category"
      label="Category"
      :items="columns"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <SelectElement
      name="layout_values"
      label="Values"
      :items="columns"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
  </template>
  <template v-else-if="['scatter_chart', 'heatmap_chart', 'pyramid_chart', 'funnel_chart', 'pareto_chart', 'waterfall_chart'].includes(props.chartType)">
    <SelectElement
      name="layout_category"
      label="X-axis"
      :items="columns"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <SelectElement
      name="layout_values"
      label="Y-axis"
      :items="columns"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <template v-if="['heatmap_chart'].includes(props.chartType)">
      <SelectElement
        name="stack_by"
        label="Stack by"
        default="none"
        :items="[
          { value: 'none', label: 'None' },
          ...columns,
        ]"
        :native="false"
        :can-clear="false"
        :can-deselect="false"
      />
    </template>
    <template v-if="['pie_chart', 'heatmap_chart', 'pyramid_chart', 'funnel_chart', 'pareto_chart', 'waterfall_chart'].includes(props.chartType)">
      <SelectElement
        name="aggregation"
        label="Aggregation"
        :items="{
          sum: 'Sum',
          mean: 'Average',
          median: 'Median',
          min: 'Minimum',
          max: 'Maximum',
          count: 'Count',
          first: 'First',
          last: 'Last',
        }"
        default="sum"
        :native="false"
        :can-clear="false"
        :can-deselect="false"
      />
    </template>
  </template>
  <template v-else-if="['gauge_chart'].includes(props.chartType)">
    <SelectElement
      name="layout_values"
      label="Values"
      :items="columns"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
  </template>
  <template v-else>
    Layout - {{ props.chartType }}
  </template>
</template>
