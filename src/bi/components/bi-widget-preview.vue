<script setup>
import { generateChartConfig } from '@sensehawk/chart-generator';
import { watchDebounced } from '@vueuse/core';
import * as echarts from 'echarts';
import { storeToRefs } from 'pinia';
import { nextTick, onBeforeUnmount } from 'vue';
import { BI_CHART_COLOR_PALETTES } from '~/bi/helpers/bi-helpers';
import { useBiStore } from '~/bi/store/bi.store';
import BiBottomDrawer from './bi-bottom-drawer.vue';

const emit = defineEmits(['continue']);

const bi_store = useBiStore();
const { chart_builder_config, chart_builder_data } = storeToRefs(bi_store);

const state = reactive({
  chart_instance: false,
  echarts_config: null,
});

function renderWidget() {
  if (chart_builder_config.value.chart_type === 'table') {
    // Probably some data processing
  }
  else {
    // TODO: Removed 'doughnut', 'funnel', 'pyramid', 'timeseries'?
    // TODO: Working of gauge chart, scatter chart, heatmap chart
    // TODO: 'mixed' chart type
    const config = {
      type: chart_builder_config.value.chart_type.replace('_chart', ''),
    };

    // Layout tab
    const values = Array.isArray(chart_builder_config.value.layout_values)
      ? chart_builder_config.value.layout_values
      : [{ value: chart_builder_config.value.layout_values }];
    if (
      (chart_builder_config.value.chart_type !== 'gauge_chart' && !chart_builder_config.value.layout_category)
      || !values.filter(item => item.value).length
      || (chart_builder_config.value.chart_type === 'heatmap_chart' && !chart_builder_config.value.stack_by)
    ) {
      state.chart_instance?.dispose?.();
      return;
    }
    config.data = {
      category: chart_builder_config.value.layout_category,
      values: values.map(item => item.value),
      stackBy: chart_builder_config.value.stack_by === 'none' ? null : chart_builder_config.value.stack_by,
    };

    // Series config
    if (chart_builder_config.value.layout_values && ['bar_chart', 'line_chart', 'area_chart'].includes(chart_builder_config.value.chart_type)) {
      config.series = {
        ...Object.values(chart_builder_config.value.layout_values).reduce((acc, item) => {
          acc[item.value] = {
            name: item.legend || item.value,
            type: item.chart_type,
            yAxisIndex: item.y_axis === 'primary' ? 0 : 1,
            color: item.color,
            stack: item.stack, // TODO: how to use?
            lineColor: item.color,
            lineWidth: Number.parseInt(item.line_width),
            lineStyle: item.line_style,
            smooth: item.line_shape === 'curved',
            prefix: item.prefix,
            suffix: item.suffix,
          };
          return acc;
        }, {}),
      };
    }

    //  Display tab
    config.layout = {
      title: chart_builder_config.value.title,
      subtitle: chart_builder_config.value.subtitle,
      // TODO: Orientation
      orientation: chart_builder_config.value.orientation,
    };
    config.legend = {
      show: chart_builder_config.value.legend_position !== 'none',
      position: chart_builder_config.value.legend_position,
    };
    config.dataValues = {
      show: chart_builder_config.value.values === 'show',
      compact: chart_builder_config.value.compact,
      precision: chart_builder_config.value.precision,
      // TODO: Global prefix and suffix?
    };
    config.styling = {
      colors: BI_CHART_COLOR_PALETTES[chart_builder_config.value.color_palette]?.colors,
    };
    config.processing = {
      aggregation: chart_builder_config.value.aggregation,
    };

    // Axes tab
    config.axes = {
      categoryName: chart_builder_config.value.category_axis_name,
      valueName: chart_builder_config.value.value_axis_name,
      secondaryValueName: chart_builder_config.value.secondary_y_axis,
      categoryLabels: chart_builder_config.value.category_tick_label,
      valueLabels: chart_builder_config.value.value_tick_label,
      secondaryValueLabels: chart_builder_config.value.secondary_value_tick_label,
      ...(!Number.isNaN(Number.parseInt(chart_builder_config.value.custom_range_min)) ? { valueMin: Number.parseInt(chart_builder_config.value.custom_range_min) } : {}),
      ...(!Number.isNaN(Number.parseInt(chart_builder_config.value.custom_range_max)) ? { valueMax: Number.parseInt(chart_builder_config.value.custom_range_max) } : {}),
      // TODO: Add secondary value min and secondary value max for certain cases
    };
    // TODO: No toggle for dual Y-axis?
    // helper.setDualYAxis(chart_builder_config.value.dual_y_axis);
    // TODO: No scale?
    // helper.setScale({
    //   primary: chart_builder_config.value.primary_scale,
    //   secondary: chart_builder_config.value.secondary_scale,
    // });

    // // Advanced tab
    config.accessibility = {
      enabled: chart_builder_config.value.accessibility_patterns,
      // TODO: New option?
      decalPatterns: false,
    };
    config.interactions = {
      // TODO: Not working as expected
      dataZoom: {
        enabled: chart_builder_config.value.data_zoom !== 'disabled',
        type: chart_builder_config.value.data_zoom,
      },
      // TODO: tooltip, brush?
    };
    // TODO: Reference lines?
    // if (chart_builder_config.value.reference_lines) {
    //   helper.setReferenceLines(chart_builder_config.value.reference_lines.map(line => ({
    //     value: line.value,
    //     label: line.label,
    //     color: line.color,
    //     lineStyle: line.line_style,
    //   })).filter(line => line.value));
    // }

    const echarts_config = generateChartConfig(chart_builder_data.value, config);
    const chartElement = document.getElementById('chart-container');
    if (!state.chart_instance) {
      state.chart_instance = echarts.init(chartElement);
    }
    else if (state.chart_instance) {
      state.chart_instance.dispose();
      state.chart_instance = echarts.init(chartElement);
    }
    state.chart_instance.setOption(echarts_config);
  }
}

function downloadWidget() {
  const imgData = state.chart_instance.getDataURL({
    type: 'png',
    pixelRatio: 2,
    backgroundColor: '#fff',
  });
  const a = document.createElement('a');
  a.href = imgData;
  a.download = 'chart.png';
  a.click();
}

watchDebounced(
  () => chart_builder_config.value,
  async () => {
    await nextTick();
    renderWidget();
  },
  { deep: true, immediate: true, debounce: 300 },
);

onBeforeUnmount(() => {
  if (state.chart_instance) {
    state.chart_instance.dispose();
    state.chart_instance = null;
  }
});
</script>

<template>
  <div class="h-full w-full flex flex-col relative">
    <div class="p-6 flex-1">
      <div v-if="chart_builder_config.chart_type === 'table'">
        TABLE
      </div>
      <div v-else id="chart-container" class="h-full w-full" />
    </div>

    <hr>
    <div class="p-6 w-full flex justify-end items-center">
      <HawkButton type="outlined" class="mr-3" @click="downloadWidget">
        <IconHawkDownloadOne />
        Download (temporary)
      </HawkButton>
      <HawkButton @click="emit('continue')">
        <IconHawkRocketTwo />
        Publish widget
      </HawkButton>
    </div>

    <BiBottomDrawer
      show-text="Show results"
      hide-text="Hide results"
    >
      <template #default>
        TABLE
      </template>
    </BiBottomDrawer>
  </div>
</template>
