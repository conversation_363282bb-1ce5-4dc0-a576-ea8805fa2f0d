import { acceptHMRUpdate, defineStore } from 'pinia';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';

const bi_query_builder = useBIQueryBuilder();
const all_tables = [
  {
    label: 'Progress History',
    columns: [{ label: 'ID', type: 'id' }, { label: 'Activity', type: 'text' }, { label: 'Subactivity', type: 'text' }, { label: 'Layer', type: 'text' }, { label: 'Sublayer', type: 'text' }, { label: 'Date', type: 'date' }, { label: 'Scope', type: 'integer' }, { label: 'Work Done', type: 'integer' }, { label: 'Temperature', type: 'float' }, { label: 'Status', type: 'text' }, { label: 'Created At', type: 'timestamp' }, { label: 'Is Active', type: 'boolean' }],
  },
  {
    label: 'Site Progress History',
    columns: [
      { label: 'Site ID', type: 'numeric' },
      { label: 'Activity', type: 'text' },
      { label: 'Completion %', type: 'numeric' },
      { label: 'Date', type: 'date' },
    ],
  },
  {
    label: 'Material Delivery Log',
    columns: [
      { label: 'Delivery ID', type: 'numeric' },
      { label: 'Material', type: 'text' },
      { label: 'Quantity', type: 'numeric' },
      { label: 'Delivery Date', type: 'date' },
      { label: 'Status', type: 'text' },
    ],
  },
  {
    label: 'Workforce Deployment',
    columns: [
      { label: 'Team ID', type: 'numeric' },
      { label: 'Crew Leader', type: 'text' },
      { label: 'No. of Workers', type: 'numeric' },
      { label: 'Assigned Task', type: 'text' },
      { label: 'Shift Date', type: 'date' },
    ],
  },
  {
    label: 'Inspection Records',
    columns: [
      { label: 'Inspection ID', type: 'numeric' },
      { label: 'Component', type: 'text' },
      { label: 'Status', type: 'text' },
      { label: 'Inspector', type: 'text' },
      { label: 'Inspection Date', type: 'date' },
    ],
  },
].map(table => ({ ...table, columns: table.columns.map(column => ({ ...column, key: bi_query_builder.constructColumnKeys(table, column) })) }));

export const useBiStore = defineStore('bi', {
  state: () => ({
    all_tables,
    selected_table: all_tables[0],
    stages: [],
    selected_stage_index: null,
    chart_builder_config: {},
    chart_builder_data: [
      { Category: 'Design', Quarter: 'Q1', Planned: 10, Actual: 8 },
      { Category: 'Design', Quarter: 'Q1', Planned: 10, Actual: 10 },
      { Category: 'Design', Quarter: 'Q2', Planned: 25, Actual: 22 },
      { Category: 'Engineering', Quarter: 'Q1', Planned: 15, Actual: 14 },
      { Category: 'Engineering', Quarter: 'Q2', Planned: 18, Actual: 16 },
      { Category: 'Design and Engineering', Quarter: 'Q2', Planned: 18, Actual: 30 },
    ],
  }),
  getters: {
  },
  actions: {
  },
});

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useBiStore, import.meta.hot));
